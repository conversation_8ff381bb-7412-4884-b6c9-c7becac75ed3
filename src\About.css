.about {
    padding: 0 7%;
    margin-bottom: 2rem;
}

.about h1 {
    font-size: 2rem;
    color: var(--text-color);
    margin-bottom: 0.75rem;
    position: relative;
    display: inline-block;
}

.box3 {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    margin-left: 0.5rem;
    margin-bottom: 2rem;
    border-radius: var(--border-radius-full);
}

.about p {
    font-size: 1.125rem;
    color: var(--text-light);
    line-height: 1.8;
    margin-top: 1.5rem;
    margin-left: 0;
    margin-right: 0;
}

.about p span {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.125rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
}

/* Tablet */
@media (max-width: 992px) {
    .about h1 {
        font-size: 1.75rem;
    }

    .box3 {
        width: 70px;
    }

    .about p {
        font-size: 1rem;
        line-height: 1.7;
    }

    .about p span {
        font-size: 1rem;
    }
}

/* Mobile */
@media (max-width: 768px) {
    .about {
        padding: 0 5%;
    }

    .about h1 {
        font-size: 1.5rem;
        margin-top: 1rem;
    }

    .box3 {
        width: 60px;
        height: 3px;
    }

    .about p {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .about p span {
        font-size: 0.95rem;
    }
}

/* Small Mobile */
@media (max-width: 480px) {
    .about h1 {
        font-size: 1.35rem;
    }

    .box3 {
        width: 50px;
    }

    .about p {
        font-size: 0.9rem;
    }

    .about p span {
        font-size: 0.9rem;
    }
}