/* Import variables from responsive.css */
:root {
  /* Modern Color Palette */
  --primary-color: #2563eb;       /* Royal Blue */
  --primary-dark: #1e40af;        /* Darker Blue */
  --primary-light: #60a5fa;       /* Lighter Blue */
  --secondary-color: #10b981;     /* Emerald Green */
  --secondary-dark: #059669;      /* Darker Green */
  --accent-color: #f43f5e;        /* Rose Red */
  --success-color: #10b981;       /* Emerald Green */
  --warning-color: #f59e0b;       /* Amber */
  --danger-color: #ef4444;        /* Red */
  --text-color: #1f2937;          /* Dark Gray */
  --text-light: #6b7280;          /* Medium Gray */
  --text-lighter: #9ca3af;        /* Light Gray */
  --bg-white: #ffffff;            /* White */
  --bg-light: #f9fafb;            /* Off White */
  --bg-gray: #f3f4f6;             /* Light Gray */
  --bg-dark: #111827;             /* Dark Gray */

  /* Design System */
  --border-radius-sm: 4px;
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-full: 9999px;

  --box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --box-shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --box-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  --transition-fast: all 0.2s ease;
  --transition: all 0.3s ease;
  --transition-slow: all 0.5s ease;
}

/* Modal backdrop */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  /* Remove transitions from here */
}

/* Modal content */
.modal-content {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  display: flex;
  box-shadow: var(--box-shadow-lg);
  max-width: 900px;
  width: 90%;
  position: relative;
  /* Remove transform and transition from here */
}

/* Close button */
.modal-close {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 30px;
  height: 30px;
  background-color: rgba(0, 0, 0, 0.1);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: var(--text-color);
  cursor: pointer;
  z-index: 10;
  transition: var(--transition);
}

.modal-close:hover {
  background-color: rgba(0, 0, 0, 0.2);
  transform: rotate(90deg);
}

/* Image container */
.imag2 {
  width: 40%;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-light);
}

.imag2 img {
  width: 100%;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  object-fit: cover;
  max-height: 400px;
}

.no-image {
  width: 100%;
  height: 200px;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius);
  color: var(--text-light);
  font-style: italic;
  border: 1px dashed #ccc;
}

/* Text content */
.modal-content2 {
  width: 60%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-white);
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid var(--bg-gray);
}

.modal-header h2 {
  color: var(--primary-color);
  font-size: 24px;
  margin: 0;
  position: relative;
  padding-bottom: 10px;
}

.modal-header h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-light));
  border-radius: 3px;
}

.modal-body {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
}

/* Info section */
.info-section {
  margin-bottom: 25px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
}

.info-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 15px;
}

.info-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  flex-shrink: 0;
}

.location-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232563eb"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>');
}

.status-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232563eb"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/></svg>');
}

.user-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232563eb"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
}

.date-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232563eb"><path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/></svg>');
}

.description-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232563eb"><path d="M19 5v14H5V5h14m1.1-2H3.9c-.5 0-.9.4-.9.9v16.2c0 .*******.9h16.2c.4 0 .9-.5.9-.9V3.9c0-.5-.5-.9-.9-.9zM11 7h6v2h-6V7zm0 4h6v2h-6v-2zm0 4h6v2h-6v-2zM7 7h2v2H7V7zm0 4h2v2H7v-2zm0 4h2v2H7v-2z"/></svg>');
}

.calendar-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232563eb"><path d="M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z"/></svg>');
}

.info-item p {
  margin: 0;
  line-height: 1.6;
  color: var(--text-color);
}

.info-item strong {
  color: var(--text-color);
  font-weight: 600;
  margin-right: 5px;
}

.description-item {
  align-items: flex-start;
}

.description-text {
  margin-top: 5px !important;
  color: var(--text-light) !important;
  line-height: 1.6;
}

/* Status selector */
.status-selector {
  display: flex;
  align-items: center;
}

.custom-select {
  position: relative;
  margin-left: 8px;
}

.select-selected {
  background-color: var(--bg-gray);
  padding: 5px;
  border-radius: var(--border-radius);
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  min-width: 100px;
  justify-content: center;
  box-shadow: var(--box-shadow-sm);
  transition: var(--transition);
}

.select-selected:hover {
  background-color: var(--bg-light);
}

.select-selected::after {
  content: '';
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid var(--text-color);
  margin-left: 10px;
}

.select-items {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-md);
  z-index: 100;
  margin-top: 5px;
  overflow: hidden;
  /* Remove animation to prevent blinking */
}

.select-items div {
  padding: 8px 10px;
  cursor: pointer;
  user-select: none;
  transition: var(--transition-fast);
  display: flex;
  justify-content: center;
}

.select-items div:hover {
  background-color: var(--bg-gray);
}

.same-as-selected {
  background-color: var(--bg-light);
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 4px 10px;
  border-radius: var(--border-radius-full);
  font-size: 14px;
  font-weight: 600;
  color: white;
  text-align: center;
}

.status-badge.open {
  background-color: var(--warning-color);
}

.status-badge.dropped {
  background-color: var(--danger-color);
}

.status-badge.closed {
  background-color: var(--success-color);
}

/* Schedule section */
.schedule-section {
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
  padding: 15px;
  margin-top: 10px;
}

.schedule-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.schedule-header h3 {
  margin: 0;
  font-size: 18px;
  color: var(--primary-color);
}

.schedule-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 15px;
}

.date-picker {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 200px;
}

.date-picker label {
  margin-right: 10px;
  font-weight: 600;
  color: var(--text-color);
}

.date-picker input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  font-size: 14px;
  color: var(--text-color);
  background-color: var(--bg-white);
  transition: var(--transition);
  flex: 1;
}

.date-picker input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}

/* Save button */
.savebutton {
  background-color: var(--primary-color);
  color: white;
  font-size: 15px;
  font-weight: 600;
  border-radius: var(--border-radius);
  padding: 10px 20px;
  border: none;
  cursor: pointer;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 140px;
  position: relative;
  overflow: hidden;
}

.savebutton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-slow);
}

.savebutton:hover::before {
  left: 100%;
}

.savebutton:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.savebutton:disabled {
  background-color: var(--text-lighter);
  cursor: not-allowed;
  transform: none;
}

.savebutton.saving {
  background-color: var(--primary-dark);
}

/* Responsive styles */
@media (max-width: 992px) {
  .modal-content {
    width: 95%;
    max-width: 800px;
  }
}

@media (max-width: 768px) {
  .modal-content {
    flex-direction: column;
  }

  .imag2, .modal-content2 {
    width: 100%;
  }

  .imag2 {
    padding: 15px;
  }

  .info-row {
    flex-direction: column;
    gap: 15px;
  }

  .schedule-content {
    flex-direction: column;
    align-items: stretch;
  }

  .savebutton {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .modal-header h2 {
    font-size: 20px;
  }

  .modal-body {
    padding: 15px;
  }

  .info-item p, .info-item strong, .date-picker label {
    font-size: 14px;
  }

  .schedule-header h3 {
    font-size: 16px;
  }

  .date-picker input {
    padding: 6px 10px;
    font-size: 14px;
  }

  .savebutton {
    font-size: 14px;
    padding: 8px 16px;
  }

  .modal-close {
    top: 10px;
    right: 10px;
    width: 25px;
    height: 25px;
    font-size: 16px;
  }
}
