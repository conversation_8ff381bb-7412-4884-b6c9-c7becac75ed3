.Footer {
    background-color: var(--bg-dark);
    color: white;
    position: relative;
    overflow: hidden;
    padding: 0;
}

.Footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    z-index: 2;
}

.Footer::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(37, 99, 235, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    z-index: 1;
}

/* Footer Content */
.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 4rem 7%;
    position: relative;
    z-index: 2;
}

/* Footer Sections */
.footer-section {
    flex: 1;
    min-width: 250px;
    margin-bottom: 2rem;
    padding-right: 2rem;
}

.footer-section h2 {
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
    color: white;
    font-weight: 600;
    position: relative;
    display: inline-block;
}

.footer-section h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius-full);
}

/* About Section */
.about p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 1.5rem;
    line-height: 1.6;
    font-size: 0.95rem;
}

.contact {
    margin-top: 1.5rem;
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.contact-icon {
    margin-right: 0.75rem;
    color: var(--primary-light);
}

/* Links Section */
.links ul {
    list-style: none;
    padding: 0;
}

.links li {
    margin-bottom: 0.75rem;
    position: relative;
    transition: var(--transition);
    padding-left: 0.5rem;
}

.links li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 1px;
    background-color: var(--primary-light);
    transition: var(--transition);
}

.links li:hover {
    padding-left: 1rem;
}

.links li:hover::before {
    width: 0.5rem;
}

.links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.95rem;
}

.links a:hover {
    color: white;
}

/* Admin Links Section */
.admin-links p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 1.5rem;
    line-height: 1.6;
    font-size: 0.95rem;
}

.admin-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.admin-btn {
    padding: 0.75rem 1.25rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition);
    display: inline-block;
    text-align: center;
}

.signin-btn {
    background-color: var(--primary-color);
    color: white;
}

.signin-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: var(--box-shadow-md);
}

.signup-btn {
    background-color: transparent;
    color: white;
    border: 1px solid var(--primary-light);
}

.signup-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
    box-shadow: var(--box-shadow-md);
}

/* Footer Bottom */
.footer-bottom {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 1.5rem 7%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
}

.social-icons {
    display: flex;
    gap: 1rem;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    transition: var(--transition);
    text-decoration: none;
}

.social-icon:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: var(--box-shadow-md);
}

.copyright {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85rem;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .footer-content {
        padding: 3rem 5%;
    }

    .footer-section {
        flex: 0 0 calc(50% - 2rem);
        padding-right: 0;
    }

    .footer-bottom {
        padding: 1.5rem 5%;
    }
}

@media (max-width: 768px) {
    .footer-content {
        padding: 3rem 5%;
    }

    .footer-section {
        flex: 0 0 100%;
        margin-bottom: 2.5rem;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .social-icons {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .admin-buttons {
        flex-direction: column;
        width: 100%;
    }

    .admin-btn {
        width: 100%;
    }
}

