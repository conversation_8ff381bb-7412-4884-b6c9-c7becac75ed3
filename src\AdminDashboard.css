:root {
  /* Modern Color Palette - Using the colors from responsive.css */
  --primary-color: #2563eb;       /* Royal Blue */
  --primary-dark: #1e40af;        /* Darker Blue */
  --primary-light: #60a5fa;       /* Lighter Blue */
  --secondary-color: #10b981;     /* Emerald Green */
  --secondary-dark: #059669;      /* Darker Green */
  --accent-color: #f43f5e;        /* Rose Red */
  --success-color: #10b981;       /* Emerald Green */
  --warning-color: #f59e0b;       /* Amber */
  --danger-color: #ef4444;        /* Red */
  --text-color: #1f2937;          /* Dark Gray */
  --text-light: #6b7280;          /* Medium Gray */
  --text-lighter: #9ca3af;        /* Light Gray */
  --bg-white: #ffffff;            /* White */
  --bg-light: #f9fafb;            /* Off White */
  --bg-gray: #f3f4f6;             /* Light Gray */
  --bg-dark: #111827;             /* Dark Gray */

  /* Design System */
  --border-radius-sm: 4px;
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-full: 9999px;

  --box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  
  --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --box-shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --box-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  --transition-fast: all 0.2s ease;
  --transition: all 0.3s ease;
  --transition-slow: all 0.5s ease;
}

/* Admin Dashboard Layout - Modern Responsive Design */
.admin-dashboard {
  display: flex;
  flex-direction: row;
  min-height: 100vh;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  position: relative;
  overflow-x: hidden;
}

/* Sidebar - Modern Responsive Design */
.admin-sidebar {
  width: 280px;
  height: calc(100vh - 40px);
  background: linear-gradient(135deg, #0e9a6c, #059669);
  border-radius: 16px;
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1000;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  overflow-y: auto;
  transition: all 0.3s ease;
}

.admin-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 3px solid #ffffff;
  padding: 10px 0;
}

.admin-buttons-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 10px;
}

.admin-buttons-container h5 {
  color: #dbd7d7;
  font-size: 22px;
  margin: 15px 0;
  font-weight: 400;
  text-align: center;
}

.admin-buttons {
  display: flex;
  flex-direction: column;
  width: 90%;
  margin: 0 auto;
}

.admin-buttons button {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  margin-bottom: 0px;
  background-color: rgba(255, 255, 255, 0.08);
  border: none;
  border-radius: var(--border-radius);
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: var(--transition);
  text-align: left;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.admin-buttons button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: var(--transition-slow);
}

.admin-buttons button:hover::before {
  left: 100%;
}

.admin-buttons button img {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  filter: brightness(0) saturate(100%);
}

.admin-buttons button:hover {
  background: linear-gradient(135deg, #1ed699, #19ce95);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.admin-buttons button.active {
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Main Content - Modern Responsive Design */
.admin-content {
  flex: 1;
  margin-left: 280px;
  padding: 20px;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  transition: all 0.3s ease;
}

.admin-header-outer {
  background: white;
  margin-left: 5px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 25px;
  overflow: hidden;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.admin-header {
  background: linear-gradient(135deg, #0e9a6c, #059669);
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-left: 5px;
  border-radius: 16px;
  color: white;
  position: relative;
  overflow: hidden;
}

.admin-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #0e9a6c, #059669);
  pointer-events: none;
}

.admin-header h4 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  letter-spacing: 0.5px;
  position: relative;
  z-index: 1;
}

.admin-content-area {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 30px;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.admin-header .logout-container {
  position: relative;
  cursor: pointer;
}

.admin-header .logout-img {
  width: 28px;
  height: 28px;
  cursor: pointer;
  transition: all 0.3s ease;
  filter: brightness(0) invert(1);
  padding: 2px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.admin-header .logout-img:hover {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 0.2);
}

.admin-header .custom-tooltip {
  visibility: hidden;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  text-align: center;
  padding: 8px 12px;
  border-radius: 8px;
  position: absolute;
  top: 50%;
  right: 100%;
  transform: translateY(-50%);
  margin-right: 10px;
  z-index: 1001;
  opacity: 0;
  transition: all 0.3s ease;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  backdrop-filter: blur(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.admin-header .custom-tooltip::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 100%;
  margin-top: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent transparent rgba(0, 0, 0, 0.9);
}

.admin-header .logout-container:hover .custom-tooltip {
  visibility: visible;
  opacity: 1;
  transform: translateY(-50%) translateX(-5px);
}

.admin-main {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 30px;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.admin-main::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(135deg, #0e9a6c, #059669);
}

.admin-main h3 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 25px;
  color: var(--primary-color);
  position: relative;
  display: inline-block;
  padding-bottom: 8px;
}

.admin-main h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-light));
  border-radius: 3px;
}

/* Mobile Navigation Toggle */
.admin-nav-toggle {
  display: none;
  position: fixed;
  /* top: 20px; */
  right: 20px;
  z-index: 1200;
  background: transparent;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.admin-nav-toggle:hover {
  transform: scale(1.05);
  box-shadow: var(--box-shadow-md);
}

.admin-nav-toggle .line {
  display: block;
  width: 24px;
  height: 2px;
  background-color: white;
  margin: 5px auto;
  transition: var(--transition);
  border-radius: 2px;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .admin-sidebar {
    transform: translateX(-110%); /* Move further left to ensure it's fully hidden */
    width: 250px;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
    z-index: 1500;
    left: 0; /* Reset the left position to ensure it's at the edge */
  }

  .admin-sidebar.show {
    transform: translateX(0);
  }

  .admin-content {
    width: 100%;
    margin-left: 0;
  }

  .admin-header {
    left: 3vw;
    width: calc(94% - 50px);
  }

  .admin-nav-toggle {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .admin-nav-toggle .line {
    margin: 3px auto;
  }

  .admin-nav-toggle.active .line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .admin-nav-toggle.active .line:nth-child(2) {
    opacity: 0;
  }

  .admin-nav-toggle.active .line:nth-child(3) {
    transform: rotate(-45deg) translate(5px, -5px);
  }

  /* Add overlay when sidebar is open */
  body:has(.admin-sidebar.show)::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1400;
  }
}

@media (max-width: 768px) {
  .admin-header {
    padding: 12px 15px;
  }

  .admin-header h4 {
    font-size: 18px;
  }

  .admin-buttons button {
    font-size: 14px;
    padding: 10px;
  }

  .admin-main h3 {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .admin-sidebar {
    width: 85%;
  }

  .admin-header {
    width: calc(94% - 40px);
    padding: 10px;
  }

  .admin-header h4 {
    font-size: 16px;
  }

  .admin-buttons-container h5 {
    font-size: 18px;
  }

  .admin-buttons button {
    font-size: 14px;
    padding: 8px 10px;
  }

  .admin-buttons button img {
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }

  .admin-main h3 {
    font-size: 18px;
    margin-bottom: 15px;
  }

  .admin-nav-toggle {
    width: 36px;
    height: 36px;
    /* top: 15px; */
    right: 15px;
  }

  .admin-nav-toggle .line {
    width: 20px;
  }
}

/* Enhanced Responsive Design - Modern Layout */
@media (max-width: 992px) {
  .admin-content {
    margin-left: 0;
    padding: 15px;
    width: 100%;
  }

  .admin-header-outer {
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #0e9a6c, #059669);
  }

  .admin-header {
    padding: 15px 20px;
  }

  .admin-header h4 {
    font-size: 1.25rem;
  }
}

@media (max-width: 768px) {
  .admin-content {
    padding: 10px;
  }

  .admin-header {
    padding: 12px 15px;
  }

  .admin-header h4 {
    font-size: 1.1rem;
  }

  .admin-content-area {
    padding: 20px 15px;
  }
}

@media (max-width: 480px) {
  .admin-content {
    padding: 5px;
  }

  .admin-header {
    padding: 10px 12px;
  }

  .admin-header h4 {
    font-size: 1rem;
  }

  .admin-content-area {
    padding: 15px 10px;
  }
}