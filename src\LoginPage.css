:root {
  /* Modern Color Palette */
  --primary-color: #2563eb;       /* Royal Blue */
  --primary-dark: #1e40af;        /* Darker Blue */
  --primary-light: #60a5fa;       /* Lighter Blue */
  --secondary-color: #10b981;     /* Emerald Green */
  --secondary-dark: #059669;      /* Darker Green */
  --accent-color: #f43f5e;        /* Rose Red */
  --success-color: #10b981;       /* Emerald Green */
  --warning-color: #f59e0b;       /* Amber */
  --danger-color: #ef4444;        /* Red */
  --text-color: #1f2937;          /* Dark Gray */
  --text-light: #6b7280;          /* Medium Gray */
  --text-lighter: #9ca3af;        /* Light Gray */
  --bg-white: #ffffff;            /* White */
  --bg-light: #f9fafb;            /* Off White */
  --bg-gray: #f3f4f6;             /* Light Gray */
  --bg-dark: #111827;             /* Dark Gray */

  /* Design System */
  --border-radius-sm: 4px;
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-full: 9999px;

  --box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --box-shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --box-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  --transition-fast: all 0.2s ease;
  --transition: all 0.3s ease;
  --transition-slow: all 0.5s ease;
}

.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-gray) 100%);
  padding: 2rem 1rem;
  position: relative;
  overflow: hidden;
}

.login-page::before {
  content: '';
  position: absolute;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, var(--primary-light) 0%, transparent 70%);
  top: -100px;
  right: -100px;
  border-radius: 50%;
  opacity: 0.4;
  z-index: 0;
}

.login-page::after {
  content: '';
  position: absolute;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, var(--secondary-color) 0%, transparent 70%);
  bottom: -100px;
  left: -100px;
  border-radius: 50%;
  opacity: 0.3;
  z-index: 0;
}

.login-container {
  width: 100%;
  max-width: 450px;
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-lg);
  overflow: hidden;
  position: relative;
  z-index: 1;
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  padding: 1.5rem;
  text-align: center;
  position: relative;
}

.login-header h2 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.login-header p {
  margin: 0.5rem 0 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.login-form {
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-color);
  font-weight: 500;
  font-size: 0.95rem;
}

.form-group input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--text-lighter);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition-fast);
  background-color: var(--bg-light);
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.forgot-password {
  text-align: right;
  margin-bottom: 1.5rem;
}

.forgot-password a {
  color: var(--primary-color);
  font-size: 0.9rem;
  text-decoration: none;
  transition: var(--transition-fast);
}

.forgot-password a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.login-button {
  width: 100%;
  padding: 0.85rem;
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-slow);
}

.login-button:hover::before {
  left: 100%;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-md);
}

.login-footer {
  text-align: center;
  padding: 1rem 2rem 2rem;
  color: var(--text-light);
}

.login-footer p {
  margin: 0;
  font-size: 0.95rem;
}

.login-footer a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition-fast);
}

.login-footer a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Responsive styles */
@media (max-width: 576px) {
  .login-container {
    max-width: 100%;
  }
  
  .login-header h2 {
    font-size: 1.5rem;
  }
  
  .login-form {
    padding: 1.5rem;
  }
}
