:root {
  /* Modern Color Palette */
  --primary-color: #cbd9f8;       /* Royal Blue */
  --primary-dark: #b5c0e4;        /* Darker Blue */
  --primary-light: #60a5fa;       /* Lighter Blue */
  --secondary-color: #10b981;     /* Emerald Green */
  --secondary-dark: #059669;      /* Darker Green */
  --accent-color: #f43f5e;        /* Rose Red */
  --success-color: #10b981;       /* Emerald Green */
  --warning-color: #f59e0b;       /* Amber */
  --danger-color: #ef4444;        /* Red */
  --text-color: #1f2937;          /* Dark Gray */
  --text-light: #6b7280;          /* Medium Gray */
  --text-lighter: #9ca3af;        /* Light Gray */
  --bg-white: #ffffff;            /* White */
  --bg-light: #f9fafb;            /* Off White */
  --bg-gray: #f3f4f6;             /* Light Gray */
  --bg-dark: #111827;             /* Dark Gray */

  /* Design System */
  --border-radius-sm: 4px;
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-full: 9999px;

  --box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --box-shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --box-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  --transition-fast: all 0.2s ease;
  --transition: all 0.3s ease;
  --transition-slow: all 0.5s ease;
}

.signin-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-gray) 100%);
  padding: 2rem 1rem;
  position: relative;
  overflow: hidden;
}

.signin-container::before {
  content: '';
  position: absolute;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, var(--primary-light) 0%, transparent 70%);
  top: -100px;
  right: -100px;
  border-radius: 50%;
  opacity: 0.4;
  z-index: 0;
}

.signin-container::after {
  content: '';
  position: absolute;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, var(--secondary-color) 0%, transparent 70%);
  bottom: -100px;
  left: -100px;
  border-radius: 50%;
  opacity: 0.3;
  z-index: 0;
}

.inner-container {
  width: 100%;
  max-width: 450px;
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-lg);
  overflow: hidden;
  position: relative;
  z-index: 1;
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.innercontainer2{
  color: #ffffff;
}
.inner-container h2 {
  /* background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)); */
  background-color: #10b981;
  color: #ffffff;
  padding: 1.5rem;
  text-align: center;
  position: relative;
  margin: 0;
  font-size: 1.75rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.signin-form {
  display: flex;
  flex-direction: column;
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-color);
  font-weight: 500;
  font-size: 0.95rem;
}

.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

/* .input-icon {
  position: absolute;
  left: 1rem;
  color: var(--text-light);
  font-size: 1rem;
} */

.form-group input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.75rem;
  border: 1px solid var(--text-lighter);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition-fast);
  background-color: var(--bg-light);
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.forgot-password {
  text-align: right;
  margin-bottom: 1.5rem;
}

.forgot-password a {
  color: var(--primary-color);
  font-size: 0.85rem;
  text-decoration: none;
  transition: var(--transition-fast);
}

.forgot-password a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.submit-button {
  width: 100%;
  padding: 0.85rem;
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-slow);
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-md);
}

.signup-link {
  text-align: center;
  margin-top: 0.5rem;
  color: var(--text-light);
  font-size: 0.95rem;
}

.signup-link:last-child {
  margin-bottom: 10px;
}

.signup-link a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition-fast);
  margin-left: 0.5rem;
}

.signup-link a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Responsive styles */
@media (max-width: 576px) {
  .inner-container {
    max-width: 100%;
  }

  .inner-container h2 {
    font-size: 1.5rem;
  }

  .signin-form {
    padding: 1.5rem;
  }
}

/* Message styles for Supabase Auth */
.message {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-weight: 500;
  text-align: center;
}

.message.success {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.message.error {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

/* Admin-specific styling */
.subtitle {
  text-align: center;
  color: #6b7280;
  margin-bottom: 2rem;
  font-size: 1rem;
}

/* Admin badge styling */
.innercontainer2 {
  position: relative;
}

.innercontainer2::after {
  content: '';
  display: block;
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #10b981, #059669);
  margin: 0.5rem auto 1rem;
  border-radius: 2px;
}

/* Divider styling */
.divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 0.5rem 0;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: linear-gradient(to right, transparent, #e5e7eb, transparent);
}

.divider span {
  padding: 0 1rem;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  background-color: var(--bg-light);
}

/* Admin signin button enhancement */
.submit-button:contains("Admin") {
  background: linear-gradient(135deg, #1f2937, #374151);
}

.submit-button:contains("Admin"):hover {
  background: linear-gradient(135deg, #374151, #4b5563);
}