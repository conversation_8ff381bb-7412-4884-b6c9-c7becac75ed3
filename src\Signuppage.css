:root {
  /* Modern Color Palette */
  --primary-color: #2563eb;       /* Royal Blue */
  --primary-dark: #1e40af;        /* Darker Blue */
  --primary-light: #60a5fa;       /* Lighter Blue */
  --secondary-color: #10b981;     /* Emerald Green */
  --secondary-dark: #059669;      /* Darker Green */
  --accent-color: #f43f5e;        /* Rose Red */
  --success-color: #10b981;       /* Emerald Green */
  --warning-color: #f59e0b;       /* Amber */
  --danger-color: #ef4444;        /* Red */
  --text-color: #1f2937;          /* Dark Gray */
  --text-light: #6b7280;          /* Medium Gray */
  --text-lighter: #9ca3af;        /* Light Gray */
  --bg-white: #ffffff;            /* White */
  --bg-light: #f9fafb;            /* Off White */
  --bg-gray: #f3f4f6;             /* Light Gray */
  --bg-dark: #111827;             /* Dark Gray */

  /* Design System */
  --border-radius-sm: 4px;
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-full: 9999px;

  --box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --box-shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --box-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  --transition-fast: all 0.2s ease;
  --transition: all 0.3s ease;
  --transition-slow: all 0.5s ease;
}

.signup-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-gray) 100%);
  padding: 2rem 1rem;
  position: relative;
  overflow: hidden;
}

.signup-container::before {
  content: '';
  position: absolute;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, var(--secondary-color) 0%, transparent 70%);
  top: -100px;
  left: -100px;
  border-radius: 50%;
  opacity: 0.4;
  z-index: 0;
}

.signup-container::after {
  content: '';
  position: absolute;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, var(--primary-light) 0%, transparent 70%);
  bottom: -100px;
  right: -100px;
  border-radius: 50%;
  opacity: 0.3;
  z-index: 0;
}

.inner-container2 {
  width: 100%;
  max-width: 550px;
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-lg);
  overflow: hidden;
  position: relative;
  z-index: 1;
  animation: fadeIn 0.5s ease-in-out;
}

/* Admin signup form should match user signup width */
.inner-container {
  width: 100%;
  max-width: 550px;
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-lg);
  padding: 0;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  animation: slideUp 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.inner-container2 h2,
.inner-container h2 {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  color: white;
  padding: 1.5rem;
  text-align: center;
  margin: 0;
  font-size: 1.75rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.signup-form {
  display: flex;
  flex-direction: column;
  padding: 2rem;
  padding-bottom: 0;
}

.form-group {
  margin-bottom: 1.5rem;
  text-align: left;
}

.form-group label, .tehsil label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-color);
  font-weight: 500;
  font-size: 0.95rem;
}

.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1rem;
  color: var(--text-light);
  font-size: 1rem;
}

.form-group input, .tehsil select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--text-lighter);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition-fast);
  background-color: var(--bg-light);
}

.form-group input:focus, .tehsil select:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.tehsil {
  margin-bottom: 1.5rem;
}

.tehsil label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-dark);
  font-size: 0.95rem;
}

.tehsil select {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  background-color: var(--bg-white);
  color: var(--text-dark);
  transition: all 0.3s ease;
  box-sizing: border-box;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

.tehsil select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.tehsil select:hover {
  border-color: var(--border-hover);
}

/* Name group styles */
.name-group {
  display: flex;
  gap: 1rem;
  margin-bottom: 0;
}

.name-group .form-group {
  flex: 1;
}

.submit-button {
  width: 100%;
  padding: 0.85rem;
  background: linear-gradient(to right, var(--secondary-color), var(--secondary-dark));
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  margin-top: 0.5rem;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-slow);
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-md);
}

.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-md);
  padding: 2rem;
}

.signup-link {
  text-align: center;
  margin-top: 0.5rem;
  color: var(--text-light);
  font-size: 0.95rem;
}

.signup-link:last-child {
  margin-bottom: 10px;
}

.signup-link a {
  color: var(--secondary-color);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition-fast);
  margin-left: 0.5rem;
}

.signup-link a:hover {
  color: var(--secondary-dark);
  text-decoration: underline;
}

/* Tablet */
@media (max-width: 992px) {
  .inner-container2,
  .inner-container {
    width: 90%;
    max-width: 600px;
  }
}

/* Mobile */
@media (max-width: 768px) {
  .signup-container {
    padding: 0;
  }

  .inner-container2,
  .inner-container {
    width: 100%;
    padding: 0.5rem;
    box-shadow: var(--box-shadow-sm);
  }

  .signup-form {
    padding: 0.5rem;
  }

  .inner-container2 h2,
  .inner-container h2 {
    font-size: 1.5rem;
    padding: 1rem;
  }

  .form-group, .tehsil {
    margin-bottom: 0.5rem;
  }

  .form-group label, .tehsil label {
    font-size: 12px;
  }

  .form-group input, .tehsil select {
    padding: 0.5rem;
    font-size: 12px;
  }

  .name-group {
    flex-direction: column;
    gap: 0.5rem;
  }

  .name-group .form-group {
    width: 100%;
  }

  .submit-button {
    padding: 0.5rem;
    font-size: 12px;
    margin-top: 0.3rem;
  }

  .signup-link {
    margin-top: 0.5rem;
    font-size: 12px;
  }
}

/* Small Mobile */
@media (max-width: 480px) {
  .inner-container2,
  .inner-container {
    padding: 0.3rem;
  }

  .signup-form {
    padding-top: 0.3rem;
    padding-left: 0.3rem;
    padding-right: 0.3rem;
    padding-bottom: 0.1rem;
  }

  .inner-container2 h2,
  .inner-container h2 {
    font-size: 1.25rem;
    padding: 0.8rem;
  }

  .form-group, .tehsil {
    margin-bottom: 0.5rem;
  }

  .form-group label, .tehsil label {
    font-size: 11px;
  }

  .form-group input, .tehsil select {
    padding: 0.4rem;
    font-size: 11px;
  }

  .submit-button {
    padding: 0.4rem;
    font-size: 11px;
  }

  .signup-link {
    font-size: 11px;
  }
}

/* Message styles for Supabase Auth */
.message {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-weight: 500;
  text-align: center;
}

.message.success {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.message.error {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

/* Admin-specific styling */
.form-hint {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
  font-style: italic;
}

.subtitle {
  text-align: center;
  color: #6b7280;
  margin-bottom: 2rem;
  font-size: 1rem;
}

/* Admin badge styling */
.innercontainer2 {
  position: relative;
}

.innercontainer2::after {
  content: '';
  display: block;
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, #10b981, #059669);
  margin: 0.5rem auto 1rem;
  border-radius: 2px;
}

/* Divider styling */
.divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 0.5rem 0;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: linear-gradient(to right, transparent, #e5e7eb, transparent);
}

.divider span {
  padding: 0 1rem;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  background-color: var(--bg-light);
}

/* Admin form enhancements */
.form-group input[type="password"]#secretKey {
  border-left: 4px solid #f59e0b;
  background-color: #fffbeb;
}

.form-group input[type="password"]#secretKey:focus {
  border-left-color: #d97706;
  background-color: #fef3c7;
}

