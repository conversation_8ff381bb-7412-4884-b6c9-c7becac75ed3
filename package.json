{"name": "clickandfix", "version": "0.1.0", "type": "module", "private": true, "dependencies": {"@supabase/supabase-js": "^2.50.3", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.2", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express-session": "^1.18.0", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "multer": "^1.4.5-lts.1", "mysql2": "^3.10.2", "nodemailer": "^6.9.14", "nodemon": "^3.1.4", "passport": "^0.7.0", "passport-local": "^1.0.0", "pg": "^8.16.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-leaflet": "^4.2.1", "react-router-dom": "^6.24.1", "react-scripts": "5.0.1", "react-spinners": "^0.14.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}