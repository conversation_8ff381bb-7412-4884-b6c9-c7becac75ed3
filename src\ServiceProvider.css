:root {
  /* Modern Color Palette */
  --primary-color: #2563eb;       /* Royal Blue */
  --primary-dark: #1e40af;        /* Darker Blue */
  --primary-light: #60a5fa;       /* Lighter Blue */
  --secondary-color: #10b981;     /* Emerald Green */
  --secondary-dark: #059669;      /* Darker Green */
  --accent-color: #f43f5e;        /* Rose Red */
  --success-color: #10b981;       /* Emerald Green */
  --warning-color: #f59e0b;       /* Amber */
  --danger-color: #ef4444;        /* Red */
  --text-color: #1f2937;          /* Dark Gray */
  --text-light: #6b7280;          /* Medium Gray */
  --text-lighter: #9ca3af;        /* Light Gray */
  --bg-white: #ffffff;            /* White */
  --bg-light: #f9fafb;            /* Off White */
  --bg-gray: #f3f4f6;             /* Light Gray */
  --bg-dark: #111827;             /* Dark Gray */

  /* Design System */
  --border-radius-sm: 4px;
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-full: 9999px;

  --box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --box-shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --box-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  --transition-fast: all 0.2s ease;
  --transition: all 0.3s ease;
  --transition-slow: all 0.5s ease;
}

.serviceheading {
  font-size: 24px;
  margin-bottom: 20px;
  color:#0e9a6c;

  position: relative;
  display: inline-block;
  padding-bottom: 8px;
  margin-left: 30px;
}

.serviceheading p {
  margin: 0;
  font-weight: 600;
}

.serviceheading::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
      background: linear-gradient(135deg, #0e9a6c, #059669);

  border-radius: 3px;
}

.card {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  justify-content: center;
  margin-top: 20px;
  width: 100%;
}

.card1 {
    background: linear-gradient(135deg, #0e9a6c, #059669);
  min-width: 300px;
  width: 55%;
  max-width: 600px;
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
  padding: 25px 0;
  margin-top: 20px;
  box-shadow: var(--box-shadow);
}

.card1 h1 {
  font-size: 28px;
  color: white;
  margin: 0 0 10px 0;
  font-weight: 600;
}

.card1 h2 {
  color: rgba(255, 255, 255, 0.9);
  font-size: 18px;
  margin: 0;
  font-weight: 400;
}

.card2 {
  display: flex;
  flex-direction: column;
  background-color: var(--bg-white);
  margin-top: 2px;
  min-width: 300px;
  width: 55%;
  max-width: 600px;
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
  text-align: left;
  margin-bottom: 20px;
  box-shadow: var(--box-shadow);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.card2-1 {
  margin: 20px;
  color: var(--text-color);
  display: flex;
  align-items: center;
  background-color: var(--bg-gray);
  padding: 15px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-sm);
}

.card2-1 p {
  font-size: 18px;
  margin: 0;
}

.numberofrequests {
  margin-right: 15px;
  font-weight: 600;
  color: var(--primary-color);
  font-size: 24px;
  background-color: rgba(37, 99, 235, 0.1);
  padding: 5px 12px;
  border-radius: var(--border-radius);
}

.resolved {
  color: var(--text-color);
}

.card2-2 {
  text-align: center;
  padding: 20px;
}

.card2-2 span {
  color: white;
  font-size: 18px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
      background: linear-gradient(135deg, #0e9a6c, #059669);

  padding: 10px 20px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.card2-2 span::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-slow);
}

.card2-2 span:hover::before {
  left: 100%;
}

.card2-2 span:hover {
  transform: translateY(-3px);
  box-shadow: var(--box-shadow-md);
}

.card2-2 span img {
  margin-right: 10px;
  width: 24px !important;
  height: 24px;
  filter: brightness(1.2);
}

.card2-2 span h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

/* Responsive styles */
@media (max-width: 992px) {
  .card1, .card2 {
    width: 70%;
  }
}

@media (max-width: 768px) {
  .card1, .card2 {
    width: 85%;
  }

  .card1 h1 {
    font-size: 24px;
  }

  .card1 h2 {
    font-size: 16px;
  }

  .card2-1 p {
    font-size: 16px;
  }

  .numberofrequests {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .card1, .card2 {
    width: 95%;
    min-width: 0;
  }

  .serviceheading {
    font-size: 20px;
    margin-left: 15px;
  }

  .card2-1 {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .numberofrequests {
    margin-right: 0;
  }

  .card2-2 span {
    padding: 8px 15px;
  }

  .card2-2 span h3 {
    font-size: 16px;
  }
}
