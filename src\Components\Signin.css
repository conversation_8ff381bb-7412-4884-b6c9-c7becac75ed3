

.Buttons {
  display: flex;
  justify-content: flex-end;
  margin-right: 7vw;
  gap: 1rem;
  margin-top: -2.5rem;
  z-index: 10;
  position: relative;
}

.Resident,
.Administration {
  font-size: 17px;
  padding: 8px 20px;
  color: white;
  border: none;
  cursor: pointer;
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: var(--transition);
  box-shadow: var(--box-shadow-sm);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.3px;
}

.Resident {
  background-color: var(--primary-color);
  position: relative;
  overflow: hidden;
}

.Resident::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-slow);
}

.Resident:hover::before {
  left: 100%;
}

.Administration {
  background-color: var(--bg-dark);
  position: relative;
  overflow: hidden;
}

.Administration::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: var(--transition-slow);
}

.Administration:hover::before {
  left: 100%;
}

.Resident:hover,
.Administration:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

/* Tablet */
@media (max-width: 992px) {
  .Buttons {
    margin-top: -3.5rem;
  }
}

/* Mobile */
@media (max-width: 768px) {
  .Buttons {
    display: none; /* Hide buttons on mobile */
  }
}

/* Small Mobile */
@media (max-width: 480px) {
  .Buttons {
    display: none; /* Hide buttons on small mobile */
  }
}