/* Import the same modern design from AllRequests.css */
@import './AllRequests.css';

/* Admin-specific enhancements */
.admin-report-card {
  position: relative;
}

.admin-report-card::before {
  content: '👨‍💼';
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(14, 154, 108, 0.1);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  z-index: 2;
}

.admin-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.edit-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.edit-btn:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Enhanced Modal for Admin */
.admin-modal-content {
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.admin-modal-body {
  max-height: 60vh;
  overflow-y: auto;
}

.modal-title-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 15px;
}

.modal-edit-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
}

.modal-edit-btn:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.admin-modal-details {
  display: grid;
  gap: 15px;
}

.document-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.document-link:hover {
  background: rgba(59, 130, 246, 0.2);
  transform: translateY(-1px);
}

.no-document {
  color: #6b7280;
  font-style: italic;
}

/* Admin-specific responsive adjustments */
@media (max-width: 768px) {
  .modal-title-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .modal-edit-btn {
    align-self: flex-start;
  }
  
  .admin-modal-content {
    margin: 10px;
    max-height: 95vh;
  }
}
