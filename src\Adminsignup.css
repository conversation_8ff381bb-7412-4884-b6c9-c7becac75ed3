
.signup-container3 {
  font-family: Arial, sans-serif;
  background: linear-gradient(135deg, #ffffff, #ffffff);
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
}

.inner-container3 {
  background: rgb(216, 215, 215) !important;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  max-width: 450px; /* Increased width */ 
  text-align: center;
}

.inner-container h2 {
  margin-bottom: 1.5rem;
  color: #ffffff;
  font-size: 30px;
  margin-left: -20px;
}

.signup-form {
  display: flex;
  flex-direction: column;
}
.tehsil label{
  display: flex;
  font-weight: bold;
}
.tehsil select{
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #8f8d8d;
  border-radius: 4px;
  box-sizing: border-box;
}
.tehsil{
  margin-bottom: 1rem;
}

.form-group {
  margin-bottom: 0.7rem;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 0.3rem;
  color: #302c2c;
  font-weight: bold;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #8f8d8d;
  border-radius: 4px;
  box-sizing: border-box;
}

.form-group input:focus {
  border-color: #817f7f;
  outline: none;
}

.name-group {
  display: flex;
  justify-content: space-between;
}

.name-group .form-group {
  width: 48%; 
}

.submit-button {
  background-color: #007bff;
  color: #fff;
  border: none;
  padding: 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.submit-button:hover {
  background-color: #0056b3;
}

@media (max-width:600px) {
  .signup-container3{
    margin-top: 10vw;
    width: 90vw;
    margin-left: 5vw;
  }
}