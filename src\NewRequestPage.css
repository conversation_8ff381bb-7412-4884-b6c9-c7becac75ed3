

.request-page-container {
  display: flex;
  justify-content: center;
  align-items: flex-start; /* Changed from center to flex-start for better mobile experience */
  min-height: 100vh; /* Use min-height instead of height for better content handling */
  background: linear-gradient(135deg, #f4f6f9 0%, #e8f4f8 100%); /* Enhanced gradient background */
  padding: 20px; /* Increased padding for better spacing */
  box-sizing: border-box; /* Ensure padding is included in height calculation */
}
.PleaseSelect{
  margin-top: 50px;
  font-size: 22px;
}
.FORMDIV{
  display: flex;
  flex-direction: row;
  gap: 5px;
}
.form-wrapper {
  width: 100%;
  max-width: 900px; /* Increased max-width for better desktop display */
  background: #fff;
  padding: 30px; /* Increased padding for better spacing */
  border-radius: 16px; /* More modern border radius */
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05); /* Enhanced shadow for depth */
  border-left: 5px solid rgb(18, 114, 18); /* Main color for accent border */
  border-top: 5px solid rgb(18, 114, 18); /* Main color for accent border */
  margin: 20px 0; /* Add vertical margin for better spacing */
  animation: slideUp 0.6s ease-out; /* Add entrance animation */
}

.form-heading {
  margin-bottom: 25px;
  margin-top: 0; /* Remove top margin for better spacing */
  font-size: 2rem; /* Use rem for better responsiveness */
  color: rgb(18, 114, 18);
  font-weight: 700; /* Slightly bolder */
  text-align: center;
  letter-spacing: 0.5px; /* Add letter spacing for elegance */
  position: relative;
}

.form-heading::after {
  content: '';
  display: block;
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, rgb(18, 114, 18), rgb(34, 197, 94));
  margin: 10px auto 0;
  border-radius: 2px;
}

.form-row {
  display: flex;
  justify-content: space-between;
  gap: 20px; /* Adding gap for spacing between items */
  margin-bottom: 20px;
}

.form-item {
  width: 90%; /* This ensures two inputs fit in one line */
}

.form-item-4 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  margin-bottom: 20px;
}

.form-item label {
  display: block;
  font-size: 16px;
  margin-bottom: 6px;
  color: #666;
  font-weight: 500;
}

.form-select,
.form-input,
.form-file,
.form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  color: #333;
  background-color: #f9f9f9;
  transition: border-color 0.3s ease;
}

.form-select:focus,
.form-input:focus,
.form-file:focus,
.form-textarea:focus {
  border-color: rgb(18, 114, 18); /* Focus effect with main color */
  outline: none;
}

.form-textarea {
  height: 100px;
  resize: vertical;
}

.submit-button {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #0e9a6c, #059669);
  color: #fff;
  border: none;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 20px;
  box-shadow: 0 4px 15px rgba(18, 114, 18, 0.3);
  letter-spacing: 0.5px;
}

.submit-button:hover {
  background: linear-gradient(135deg, #3bd7a3, #1dc791);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(18, 114, 18, 0.4);
}

.submit-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(18, 114, 18, 0.3);
}

/* Adding hover and focus effects for inputs */
.form-input:hover,
.form-select:hover,
.form-textarea:hover {
  border-color: rgb(18, 114, 18);
}


.request-container {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/*.request### CSS for the Request Pages (Request.css)

Here’s a CSS snippet to style the request forms uniformly:

```css*/
.request-container {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.request-container h1 {
  font-size: 24px;
  margin-bottom: 10px;
}

.request-container p {
  font-size: 16px;
  margin-bottom: 20px;
}

.request-container ul {
  margin-bottom: 20px;
}

.request-container li {
  margin-bottom: 5px;
}

.request-container input[type="file"] {
  margin-bottom: 10px;
}

.request-container button {
  padding: 10px 15px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.request-container button:hover {
  background-color: #0056b3;
}


/* Animation for form entrance */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced responsive behavior */
@media (max-width: 768px) {
  .request-page-container {
    padding: 15px;
    align-items: flex-start; /* Ensure proper alignment on mobile */
  }

  .form-wrapper {
    padding: 20px; /* Reduced padding for smaller screens */
    margin: 10px 0;
    border-radius: 12px;
  }

  .form-row {
    flex-direction: column; /* Stack inputs on smaller screens */
    gap: 0;
  }

  .form-item {
    width: 100%; /* Full width for smaller screens */
    margin-bottom: 15px;
  }

  .FORMDIV{
    display: flex;
    flex-direction: column;
  }

  .form-heading {
    font-size: 1.75rem; /* Smaller heading on mobile */
  }
}

@media (max-width: 480px) {
  .request-page-container {
    padding: 10px;
  }

  .form-wrapper {
    padding: 15px;
    margin: 5px 0;
  }

  .form-heading {
    font-size: 1.5rem;
  }
}

/* Ensure full height on all screen sizes */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
}

#root {
  min-height: 100vh;
}
